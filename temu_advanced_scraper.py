#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Temu高级数据抓取器
结合多种方法获取商店和商品数据
"""

import json
import time
import random
import requests
import re
from typing import Dict, List, Optional, Union
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException

class TemuAdvancedScraper:
    def __init__(self, cookie_file: str = 'cookie.json', headless: bool = False):
        self.cookie_file = cookie_file
        self.headless = headless
        self.driver = None
        self.session = requests.Session()
        self.cookies_data = self.load_cookies_from_file()
        self.setup_session()
        
    def load_cookies_from_file(self) -> List[Dict]:
        """从文件加载cookies"""
        try:
            with open(self.cookie_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            print(f"加载cookies文件失败: {e}")
            return []
    
    def setup_session(self):
        """设置requests会话"""
        # 设置请求头
        self.session.headers.update({
            'accept': 'application/json, text/plain, */*',
            'accept-encoding': 'gzip, deflate, br',
            'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8,zh-TW;q=0.7',
            'cache-control': 'no-cache',
            'content-type': 'application/json;charset=UTF-8',
            'origin': 'https://www.temu.com',
            'pragma': 'no-cache',
            'referer': 'https://www.temu.com/',
            'sec-ch-ua': '"Chromium";v="134", "Not:A-Brand";v="24", "Google Chrome";v="134"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"Windows"',
            'sec-fetch-dest': 'empty',
            'sec-fetch-mode': 'cors',
            'sec-fetch-site': 'same-origin',
            'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'x-requested-with': 'XMLHttpRequest'
        })
        
        # 设置cookies
        for cookie in self.cookies_data:
            self.session.cookies.set(
                cookie['name'], 
                cookie['value'], 
                domain=cookie['domain'].lstrip('.'),
                path=cookie['path']
            )
    
    def setup_selenium(self):
        """设置Selenium浏览器"""
        if self.driver:
            return
            
        chrome_options = Options()
        if self.headless:
            chrome_options.add_argument('--headless')
        
        # 反检测设置
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')
        chrome_options.add_argument('--disable-blink-features=AutomationControlled')
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        chrome_options.add_experimental_option('useAutomationExtension', False)
        chrome_options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36')
        
        self.driver = webdriver.Chrome(options=chrome_options)
        self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
        
        # 加载cookies到浏览器
        self.driver.get("https://www.temu.com")
        time.sleep(2)
        
        for cookie in self.cookies_data:
            try:
                cookie_dict = {
                    'name': cookie['name'],
                    'value': cookie['value'],
                    'domain': cookie['domain'],
                    'path': cookie['path'],
                    'secure': cookie.get('secure', False)
                }
                if 'expirationDate' in cookie:
                    cookie_dict['expiry'] = int(cookie['expirationDate'])
                self.driver.add_cookie(cookie_dict)
            except Exception as e:
                continue
    
    def extract_mall_id_from_url(self, url: str) -> Optional[str]:
        """从URL中提取商店ID"""
        patterns = [
            r'm-(\d+)',
            r'mall[_-]?id[=:](\d+)',
            r'/m/(\d+)',
        ]
        
        for pattern in patterns:
            match = re.search(pattern, url)
            if match:
                return match.group(1)
        return None
    
    def try_api_request(self, mall_id: str, page: int = 1, page_size: int = 20) -> Optional[Dict]:
        """尝试API请求"""
        print("尝试API请求...")
        
        # 生成请求参数
        list_id = ''.join(random.choice('abcdefghijklmnopqrstuvwxyz0123456789') for _ in range(21))
        
        payload = {
            "mallId": mall_id,
            "mainGoodsIds": ["1"],
            "source_page_sn": "10013",
            "mall_id": mall_id,
            "main_goods_ids": ["1"],
            "filter_items": "",
            "page_number": page,
            "page_size": page_size,
            "list_id": list_id,
            "scene_code": "mall_rule",
            "page_sn": 10040,
            "page_el_sn": 201265,
            "source": 10018,
            "anti_content": "1"
        }
        
        # 更新referer
        self.session.headers['referer'] = f'https://www.temu.com/us-zh-Hans/mug-love-m-{mall_id}.html'
        
        try:
            url = "https://www.temu.com/us-zh-Hans/api/bg/circle/c/mall/mallInfoWithGoodsList"
            response = self.session.post(url, json=payload, timeout=30)
            
            print(f"API请求状态码: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    print("API请求成功!")
                    return data
                else:
                    print(f"API返回错误: {data}")
            else:
                print(f"API请求失败: {response.text[:200]}")
                
        except Exception as e:
            print(f"API请求异常: {e}")
        
        return None
    
    def try_selenium_extraction(self, url: str) -> Dict:
        """使用Selenium提取数据"""
        print("使用Selenium提取数据...")
        
        self.setup_selenium()
        
        try:
            self.driver.get(url)
            time.sleep(8)
            
            # 检查页面是否正常加载
            if "登录" in self.driver.page_source or "login" in self.driver.current_url.lower():
                print("检测到登录页面，刷新重试...")
                self.driver.refresh()
                time.sleep(5)
            
            # 提取数据
            result = {
                'store_info': self._extract_store_info_selenium(),
                'products': self._extract_products_selenium(),
                'page_title': self.driver.title,
                'current_url': self.driver.current_url
            }
            
            # 尝试执行JavaScript获取更多数据
            try:
                js_data = self.driver.execute_script("""
                    // 尝试从页面中找到数据
                    var data = {};
                    
                    // 查找粉丝数
                    var followersElements = document.querySelectorAll('*');
                    for (var i = 0; i < followersElements.length; i++) {
                        var text = followersElements[i].textContent || '';
                        if (text.match(/\\d+.*粉丝|\\d+.*followers/i)) {
                            data.followers_text = text;
                            break;
                        }
                    }
                    
                    // 查找销量
                    for (var i = 0; i < followersElements.length; i++) {
                        var text = followersElements[i].textContent || '';
                        if (text.match(/\\d+.*已售|\\d+.*sold/i)) {
                            data.sold_text = text;
                            break;
                        }
                    }
                    
                    // 查找商品数量
                    var productElements = document.querySelectorAll('[class*="goods"], [class*="product"], [class*="item"]');
                    data.product_elements_count = productElements.length;
                    
                    return data;
                """)
                
                if js_data:
                    result['js_extracted'] = js_data
                    
            except Exception as e:
                print(f"JavaScript提取失败: {e}")
            
            return result
            
        except Exception as e:
            print(f"Selenium提取失败: {e}")
            return {'error': str(e)}
    
    def _extract_store_info_selenium(self) -> Dict:
        """使用Selenium提取商店信息"""
        store_info = {}
        
        try:
            # 商店名称
            name_selectors = [
                'h1', '.store-name', '[data-testid="store-name"]',
                '.mall-name', '.shop-name'
            ]
            
            for selector in name_selectors:
                try:
                    element = self.driver.find_element(By.CSS_SELECTOR, selector)
                    text = element.text.strip()
                    if text and text != "登录 / 注册":
                        store_info['store_name'] = text
                        break
                except:
                    continue
            
            # 使用正则表达式从页面源码中提取数据
            page_source = self.driver.page_source
            
            # 提取粉丝数
            followers_patterns = [
                r'(\d+(?:,\d+)*)\s*粉丝',
                r'(\d+(?:,\d+)*)\s*followers',
                r'"followNum"\s*:\s*(\d+)',
                r'粉丝.*?(\d+(?:,\d+)*)'
            ]
            
            for pattern in followers_patterns:
                match = re.search(pattern, page_source, re.IGNORECASE)
                if match:
                    store_info['followers'] = int(match.group(1).replace(',', ''))
                    break
            
            # 提取销量
            sold_patterns = [
                r'(\d+(?:,\d+)*)\s*已售',
                r'(\d+(?:,\d+)*)\s*sold',
                r'"soldNum"\s*:\s*(\d+)',
                r'已售.*?(\d+(?:,\d+)*)'
            ]
            
            for pattern in sold_patterns:
                match = re.search(pattern, page_source, re.IGNORECASE)
                if match:
                    store_info['total_sold'] = int(match.group(1).replace(',', ''))
                    break
                    
        except Exception as e:
            print(f"提取商店信息失败: {e}")
        
        return store_info
    
    def _extract_products_selenium(self) -> List[Dict]:
        """使用Selenium提取商品信息"""
        products = []
        
        try:
            # 等待页面加载
            time.sleep(3)
            
            # 尝试多种商品选择器
            product_selectors = [
                '.goods-item', '.product-item', '.item-card',
                '[class*="goods"]', '[class*="product"]', '[class*="item"]'
            ]
            
            product_elements = []
            for selector in product_selectors:
                try:
                    elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    if len(elements) > len(product_elements):
                        product_elements = elements
                except:
                    continue
            
            print(f"找到 {len(product_elements)} 个商品元素")
            
            # 如果没找到商品元素，尝试从页面源码中提取
            if not product_elements:
                page_source = self.driver.page_source
                # 查找商品相关的JSON数据
                json_patterns = [
                    r'"goodsList"\s*:\s*(\[.*?\])',
                    r'"goods"\s*:\s*(\[.*?\])',
                    r'"products"\s*:\s*(\[.*?\])'
                ]
                
                for pattern in json_patterns:
                    match = re.search(pattern, page_source, re.DOTALL)
                    if match:
                        try:
                            goods_data = json.loads(match.group(1))
                            for item in goods_data[:20]:  # 限制20个
                                product = {
                                    'title': item.get('goodsName', item.get('title', '')),
                                    'price': str(item.get('price', item.get('priceInfo', {}).get('price', ''))),
                                    'image_url': item.get('image', item.get('imageUrl', '')),
                                    'sold_num': item.get('soldNum', 0)
                                }
                                if product['title']:
                                    products.append(product)
                            break
                        except:
                            continue
            
        except Exception as e:
            print(f"提取商品列表失败: {e}")
        
        return products
    
    def get_store_data(self, url: str) -> Dict:
        """获取商店数据的主方法"""
        mall_id = self.extract_mall_id_from_url(url)
        if not mall_id:
            return {'error': '无法从URL中提取商店ID'}
        
        print(f"提取到商店ID: {mall_id}")
        
        result = {
            'mall_id': mall_id,
            'url': url,
            'extraction_time': time.time(),
            'methods_tried': []
        }
        
        # 方法1: 尝试API请求
        api_data = self.try_api_request(mall_id)
        if api_data:
            result['api_data'] = api_data
            result['methods_tried'].append('api_success')
            
            # 解析API数据
            if api_data.get('success') and api_data.get('result'):
                api_result = api_data['result']
                
                # 商店信息
                mall_info = api_result.get('mallInfo', {})
                if mall_info:
                    result['store_info'] = {
                        'store_name': mall_info.get('mallName', ''),
                        'followers': mall_info.get('followNum', 0),
                        'total_sold': mall_info.get('soldNum', 0),
                        'rating': mall_info.get('rating', 0),
                        'store_logo': mall_info.get('mallLogo', '')
                    }
                
                # 商品列表
                goods_list = api_result.get('goodsList', [])
                if goods_list:
                    result['products'] = []
                    for goods in goods_list:
                        product = {
                            'product_id': goods.get('goodsId', ''),
                            'title': goods.get('goodsName', ''),
                            'price': str(goods.get('priceInfo', {}).get('price', '')),
                            'image_url': goods.get('image', ''),
                            'sold_num': goods.get('soldNum', 0),
                            'rating': goods.get('rating', 0)
                        }
                        result['products'].append(product)
                
                return result
        else:
            result['methods_tried'].append('api_failed')
        
        # 方法2: 使用Selenium
        selenium_data = self.try_selenium_extraction(url)
        if 'error' not in selenium_data:
            result.update(selenium_data)
            result['methods_tried'].append('selenium_success')
        else:
            result['methods_tried'].append('selenium_failed')
            result['selenium_error'] = selenium_data['error']
        
        return result
    
    def save_data(self, data: Dict, filename: str = 'temu_advanced_data.json'):
        """保存数据"""
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            print(f"数据已保存到 {filename}")
        except Exception as e:
            print(f"保存数据失败: {e}")
    
    def close(self):
        """关闭资源"""
        if self.driver:
            self.driver.quit()
        self.session.close()

def main():
    """主函数"""
    url = "https://www.temu.com/us-zh-Hans/mug-love-m-634418221696617.html?goods_id=601100243104197"
    
    scraper = TemuAdvancedScraper(headless=False)
    
    try:
        print("开始高级数据提取...")
        data = scraper.get_store_data(url)
        
        print(f"\n=== 提取结果 ===")
        print(f"商店ID: {data.get('mall_id', 'N/A')}")
        print(f"尝试的方法: {', '.join(data.get('methods_tried', []))}")
        
        # 显示商店信息
        store_info = data.get('store_info', {})
        if store_info:
            print(f"\n商店信息:")
            print(f"  名称: {store_info.get('store_name', 'N/A')}")
            print(f"  粉丝数: {store_info.get('followers', 'N/A')}")
            print(f"  总销量: {store_info.get('total_sold', 'N/A')}")
            print(f"  评分: {store_info.get('rating', 'N/A')}")
        
        # 显示商品信息
        products = data.get('products', [])
        if products:
            print(f"\n商品列表 ({len(products)}个):")
            for i, product in enumerate(products[:5], 1):
                print(f"  {i}. {product.get('title', 'N/A')}")
                print(f"     价格: {product.get('price', 'N/A')}")
                print(f"     销量: {product.get('sold_num', 'N/A')}")
        
        # 显示JavaScript提取的数据
        js_data = data.get('js_extracted', {})
        if js_data:
            print(f"\nJavaScript提取的数据:")
            for key, value in js_data.items():
                print(f"  {key}: {value}")
        
        # 保存数据
        scraper.save_data(data)
        
        if 'error' in data:
            print(f"\n错误: {data['error']}")
    
    finally:
        print("\n按回车键关闭...")
        input()
        scraper.close()

if __name__ == "__main__":
    main()
