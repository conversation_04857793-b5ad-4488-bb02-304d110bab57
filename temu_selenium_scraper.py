#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Temu Selenium 爬虫
使用浏览器自动化绕过风控机制
"""

import time
import json
import re
from typing import Dict, List, Optional
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import TimeoutException, NoSuchElementException

class TemuSeleniumScraper:
    def __init__(self, headless: bool = False):
        self.driver = None
        self.headless = headless
        self.setup_driver()
    
    def setup_driver(self):
        """设置Chrome浏览器"""
        chrome_options = Options()
        
        if self.headless:
            chrome_options.add_argument('--headless')
        
        # 反检测设置
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')
        chrome_options.add_argument('--disable-blink-features=AutomationControlled')
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        chrome_options.add_experimental_option('useAutomationExtension', False)
        
        # 设置用户代理
        chrome_options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36')
        
        try:
            self.driver = webdriver.Chrome(options=chrome_options)
            
            # 执行反检测脚本
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            
        except Exception as e:
            print(f"浏览器启动失败: {e}")
            print("请确保已安装Chrome浏览器和ChromeDriver")
            raise
    
    def get_store_data(self, url: str) -> Dict:
        """
        获取商店数据
        """
        try:
            print(f"正在访问页面: {url}")
            self.driver.get(url)
            
            # 等待页面加载
            time.sleep(5)
            
            # 等待关键元素加载
            wait = WebDriverWait(self.driver, 20)
            
            # 提取商店基本信息
            store_data = self._extract_store_info()
            
            # 提取商品列表
            products_data = self._extract_products_info()
            
            # 尝试通过网络请求获取更多数据
            api_data = self._intercept_api_data()
            
            result = {
                'store_info': store_data,
                'products': products_data,
                'api_data': api_data,
                'extraction_time': time.time()
            }
            
            return result
            
        except Exception as e:
            print(f"数据提取失败: {e}")
            return {'error': str(e)}
    
    def _extract_store_info(self) -> Dict:
        """提取商店信息"""
        store_info = {}
        
        try:
            # 商店名称
            try:
                store_name_element = self.driver.find_element(By.CSS_SELECTOR, '[data-testid="store-name"], .store-name, h1')
                store_info['store_name'] = store_name_element.text.strip()
            except NoSuchElementException:
                store_info['store_name'] = ''
            
            # 粉丝数 - 尝试多种选择器
            followers_selectors = [
                '[data-testid="followers"]',
                '.followers-count',
                '*[contains(text(), "粉丝")]',
                '*[contains(text(), "followers")]'
            ]
            
            for selector in followers_selectors:
                try:
                    element = self.driver.find_element(By.CSS_SELECTOR, selector)
                    text = element.text
                    followers = self._extract_number_from_text(text)
                    if followers is not None:
                        store_info['followers'] = followers
                        break
                except:
                    continue
            
            # 销量 - 尝试多种选择器
            sold_selectors = [
                '[data-testid="sold"]',
                '.sold-count',
                '*[contains(text(), "已售")]',
                '*[contains(text(), "sold")]'
            ]
            
            for selector in sold_selectors:
                try:
                    element = self.driver.find_element(By.CSS_SELECTOR, selector)
                    text = element.text
                    sold = self._extract_number_from_text(text)
                    if sold is not None:
                        store_info['total_sold'] = sold
                        break
                except:
                    continue
            
            # 通过页面源码查找数据
            page_source = self.driver.page_source
            
            # 使用正则表达式查找粉丝数和销量
            followers_match = re.search(r'(\d+(?:,\d+)*)\s*粉丝', page_source)
            if followers_match and 'followers' not in store_info:
                store_info['followers'] = int(followers_match.group(1).replace(',', ''))
            
            sold_match = re.search(r'(\d+(?:,\d+)*)\s*已售', page_source)
            if sold_match and 'total_sold' not in store_info:
                store_info['total_sold'] = int(sold_match.group(1).replace(',', ''))
            
        except Exception as e:
            print(f"提取商店信息失败: {e}")
        
        return store_info
    
    def _extract_products_info(self) -> List[Dict]:
        """提取商品信息"""
        products = []
        
        try:
            # 等待商品列表加载
            time.sleep(3)
            
            # 尝试多种商品容器选择器
            product_selectors = [
                '.goods-item',
                '.product-item',
                '[data-testid="product"]',
                '.item-card'
            ]
            
            product_elements = []
            for selector in product_selectors:
                try:
                    elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    if elements:
                        product_elements = elements
                        break
                except:
                    continue
            
            print(f"找到 {len(product_elements)} 个商品元素")
            
            for element in product_elements[:20]:  # 限制前20个商品
                try:
                    product_info = {}
                    
                    # 商品标题
                    try:
                        title_element = element.find_element(By.CSS_SELECTOR, '.title, .name, .goods-name, h3, h4')
                        product_info['title'] = title_element.text.strip()
                    except:
                        product_info['title'] = ''
                    
                    # 商品图片
                    try:
                        img_element = element.find_element(By.CSS_SELECTOR, 'img')
                        product_info['image_url'] = img_element.get_attribute('src')
                    except:
                        product_info['image_url'] = ''
                    
                    # 价格
                    try:
                        price_element = element.find_element(By.CSS_SELECTOR, '.price, .current-price, .sale-price')
                        product_info['price'] = price_element.text.strip()
                    except:
                        product_info['price'] = ''
                    
                    # 销量
                    try:
                        sold_element = element.find_element(By.CSS_SELECTOR, '.sold, .sales')
                        sold_text = sold_element.text
                        product_info['sold_num'] = self._extract_number_from_text(sold_text)
                    except:
                        product_info['sold_num'] = 0
                    
                    if product_info['title']:  # 只添加有标题的商品
                        products.append(product_info)
                        
                except Exception as e:
                    print(f"提取单个商品信息失败: {e}")
                    continue
            
        except Exception as e:
            print(f"提取商品列表失败: {e}")
        
        return products
    
    def _intercept_api_data(self) -> Optional[Dict]:
        """尝试拦截API数据"""
        try:
            # 执行JavaScript来监听网络请求
            script = """
            window.temuApiData = null;
            
            // 重写fetch方法
            const originalFetch = window.fetch;
            window.fetch = function(...args) {
                return originalFetch.apply(this, args).then(response => {
                    if (args[0] && args[0].includes && args[0].includes('mallInfoWithGoodsList')) {
                        response.clone().json().then(data => {
                            window.temuApiData = data;
                        }).catch(e => console.log('Parse JSON failed:', e));
                    }
                    return response;
                });
            };
            
            // 重写XMLHttpRequest
            const originalXHR = window.XMLHttpRequest;
            window.XMLHttpRequest = function() {
                const xhr = new originalXHR();
                const originalOpen = xhr.open;
                xhr.open = function(method, url, ...args) {
                    if (url.includes('mallInfoWithGoodsList')) {
                        const originalOnLoad = xhr.onload;
                        xhr.onload = function() {
                            try {
                                const data = JSON.parse(xhr.responseText);
                                window.temuApiData = data;
                            } catch (e) {
                                console.log('Parse XHR JSON failed:', e);
                            }
                            if (originalOnLoad) originalOnLoad.apply(this, arguments);
                        };
                    }
                    return originalOpen.apply(this, [method, url, ...args]);
                };
                return xhr;
            };
            
            return 'Network interception setup complete';
            """
            
            self.driver.execute_script(script)
            
            # 等待一段时间让API请求触发
            time.sleep(5)
            
            # 尝试滚动页面触发更多请求
            self.driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
            time.sleep(3)
            
            # 获取拦截到的数据
            api_data = self.driver.execute_script("return window.temuApiData;")
            
            return api_data
            
        except Exception as e:
            print(f"API数据拦截失败: {e}")
            return None
    
    def _extract_number_from_text(self, text: str) -> Optional[int]:
        """从文本中提取数字"""
        if not text:
            return None
        
        # 移除逗号和其他分隔符
        text = text.replace(',', '').replace(' ', '')
        
        # 查找数字
        match = re.search(r'(\d+)', text)
        if match:
            return int(match.group(1))
        
        return None
    
    def save_data(self, data: Dict, filename: str = 'temu_selenium_data.json'):
        """保存数据到文件"""
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            print(f"数据已保存到 {filename}")
        except Exception as e:
            print(f"保存数据失败: {e}")
    
    def close(self):
        """关闭浏览器"""
        if self.driver:
            self.driver.quit()

def main():
    """主函数"""
    url = "https://www.temu.com/us-zh-Hans/mug-love-m-634418221696617.html?goods_id=601100243104197"
    
    scraper = TemuSeleniumScraper(headless=False)  # 设置为False以便观察过程
    
    try:
        print("开始提取Temu数据...")
        data = scraper.get_store_data(url)
        
        if 'error' not in data:
            print("\n=== 提取结果 ===")
            
            # 显示商店信息
            store_info = data.get('store_info', {})
            print(f"商店名称: {store_info.get('store_name', 'N/A')}")
            print(f"粉丝数: {store_info.get('followers', 'N/A')}")
            print(f"总销量: {store_info.get('total_sold', 'N/A')}")
            
            # 显示商品信息
            products = data.get('products', [])
            print(f"\n商品数量: {len(products)}")
            
            for i, product in enumerate(products[:5], 1):
                print(f"{i}. {product.get('title', 'N/A')}")
                print(f"   价格: {product.get('price', 'N/A')}")
                print(f"   销量: {product.get('sold_num', 'N/A')}")
            
            # 显示API数据
            if data.get('api_data'):
                print(f"\n成功拦截到API数据!")
                api_data = data['api_data']
                if api_data.get('success'):
                    result = api_data.get('result', {})
                    mall_info = result.get('mallInfo', {})
                    goods_list = result.get('goodsList', [])
                    
                    print(f"API - 粉丝数: {mall_info.get('followNum', 'N/A')}")
                    print(f"API - 总销量: {mall_info.get('soldNum', 'N/A')}")
                    print(f"API - 商品数量: {len(goods_list)}")
            
            # 保存数据
            scraper.save_data(data)
            
        else:
            print(f"提取失败: {data['error']}")
    
    finally:
        scraper.close()

if __name__ == "__main__":
    main()
