#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Temu数据抓取器
解决风控问题并获取商店和商品数据
"""

import requests
import json
import time
import random
import urllib.parse
from typing import Dict, List, Optional

class TemuScraper:
    def __init__(self):
        self.session = requests.Session()
        self.base_url = "https://www.temu.com"
        
        # 更完整的请求头，模拟真实浏览器
        self.headers = {
            'accept': 'application/json, text/plain, */*',
            'accept-encoding': 'gzip, deflate, br',
            'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8,zh-TW;q=0.7',
            'cache-control': 'no-cache',
            'content-type': 'application/json;charset=UTF-8',
            'origin': 'https://www.temu.com',
            'pragma': 'no-cache',
            'priority': 'u=1, i',
            'referer': 'https://www.temu.com/',
            'sec-ch-ua': '"Chromium";v="134", "Not:A-Brand";v="24", "Google Chrome";v="134"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"Windows"',
            'sec-fetch-dest': 'empty',
            'sec-fetch-mode': 'cors',
            'sec-fetch-site': 'same-origin',
            'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'x-requested-with': 'XMLHttpRequest'
        }
        
        self.session.headers.update(self.headers)
    
    def set_cookies_from_string(self, cookie_string: str):
        """
        从Cookie字符串设置会话Cookie
        """
        # 清除现有cookies
        self.session.cookies.clear()
        
        # 解析并设置新cookies
        for item in cookie_string.split('; '):
            if '=' in item:
                key, value = item.split('=', 1)
                # URL解码cookie值
                value = urllib.parse.unquote(value)
                self.session.cookies.set(key, value, domain='.temu.com')
    
    def extract_mall_id_from_url(self, url: str) -> Optional[str]:
        """
        从URL中提取商店ID
        """
        import re
        # 多种模式匹配
        patterns = [
            r'm-(\d+)',  # m-数字
            r'mall[_-]?id[=:](\d+)',  # mall_id=数字
            r'/m/(\d+)',  # /m/数字
        ]
        
        for pattern in patterns:
            match = re.search(pattern, url)
            if match:
                return match.group(1)
        
        return None
    
    def get_mall_info_and_goods(self, mall_id: str, page: int = 1, page_size: int = 20) -> Optional[Dict]:
        """
        获取商店信息和商品列表
        """
        # 生成请求参数
        list_id = self._generate_list_id()
        
        payload = {
            "mallId": mall_id,
            "mainGoodsIds": ["1"],
            "source_page_sn": "10013",
            "mall_id": mall_id,
            "main_goods_ids": ["1"],
            "filter_items": "",
            "page_number": page,
            "page_size": page_size,
            "list_id": list_id,
            "scene_code": "mall_rule",
            "page_sn": 10040,
            "page_el_sn": 201265,
            "source": 10018,
            "anti_content": "1"
        }
        
        # 设置特定的请求头
        headers = self.headers.copy()
        headers['referer'] = f'https://www.temu.com/us-zh-Hans/mug-love-m-{mall_id}.html'
        
        try:
            url = f"{self.base_url}/us-zh-Hans/api/bg/circle/c/mall/mallInfoWithGoodsList"
            
            # 添加延迟，避免请求过快
            time.sleep(random.uniform(1, 3))
            
            response = self.session.post(url, json=payload, headers=headers, timeout=30)
            
            print(f"请求状态码: {response.status_code}")
            print(f"响应内容预览: {response.text[:200]}")
            
            if response.status_code == 200:
                return response.json()
            else:
                print(f"请求失败: {response.status_code}")
                print(f"响应头: {dict(response.headers)}")
                return None
                
        except Exception as e:
            print(f"请求异常: {str(e)}")
            return None
    
    def _generate_list_id(self) -> str:
        """生成随机list_id"""
        chars = 'abcdefghijklmnopqrstuvwxyz0123456789'
        return ''.join(random.choice(chars) for _ in range(21))
    
    def parse_response_data(self, response_data: Dict) -> Dict:
        """
        解析API响应数据
        """
        if not response_data or not response_data.get('success'):
            return {'error': '响应数据无效或请求失败'}
        
        result = response_data.get('result', {})
        parsed_data = {}
        
        # 解析商店信息
        mall_info = result.get('mallInfo', {})
        if mall_info:
            parsed_data['store_info'] = {
                'store_name': mall_info.get('mallName', ''),
                'store_id': mall_info.get('mallId', ''),
                'followers': mall_info.get('followNum', 0),  # 粉丝数
                'total_sold': mall_info.get('soldNum', 0),   # 总销量
                'store_logo': mall_info.get('mallLogo', ''),
                'description': mall_info.get('mallDesc', ''),
                'rating': mall_info.get('rating', 0),
                'review_count': mall_info.get('reviewNum', 0)
            }
        
        # 解析商品列表
        goods_list = result.get('goodsList', [])
        parsed_data['products'] = []
        
        for goods in goods_list:
            product_info = {
                'product_id': goods.get('goodsId', ''),
                'title': goods.get('goodsName', ''),
                'description': goods.get('goodsDesc', ''),
                'image_url': goods.get('image', ''),
                'images': goods.get('images', []),  # 多张图片
                'price': self._extract_price(goods.get('priceInfo', {})),
                'original_price': self._extract_original_price(goods.get('priceInfo', {})),
                'discount': goods.get('discount', ''),
                'sold_num': goods.get('soldNum', 0),
                'rating': goods.get('rating', 0),
                'review_count': goods.get('reviewNum', 0),
                'product_url': goods.get('goodsUrl', ''),
                'category': goods.get('category', ''),
                'tags': goods.get('tags', [])
            }
            parsed_data['products'].append(product_info)
        
        # 添加分页信息
        parsed_data['pagination'] = {
            'current_page': result.get('pageNumber', 1),
            'page_size': result.get('pageSize', 20),
            'total_count': result.get('totalCount', 0),
            'has_more': result.get('hasMore', False)
        }
        
        return parsed_data
    
    def _extract_price(self, price_info: Dict) -> str:
        """提取价格信息"""
        if not price_info:
            return ''
        
        # 尝试多种价格字段
        price_fields = ['price', 'salePrice', 'currentPrice', 'minPrice']
        for field in price_fields:
            if field in price_info and price_info[field]:
                return str(price_info[field])
        
        return ''
    
    def _extract_original_price(self, price_info: Dict) -> str:
        """提取原价信息"""
        if not price_info:
            return ''
        
        # 尝试多种原价字段
        price_fields = ['originalPrice', 'marketPrice', 'maxPrice']
        for field in price_fields:
            if field in price_info and price_info[field]:
                return str(price_info[field])
        
        return ''

def main():
    """
    主函数 - 演示如何使用
    """
    # 初始化爬虫
    scraper = TemuScraper()
    
    # 设置Cookie（需要从浏览器获取最新的）
    cookie_string = """region=211; timezone=Asia%2FHong_Kong; gmp_temu_token=EVIPzCVqVuHegqxRTi6uTh+lCO4JjZ2iskrSNW9KC6utVrUN3EYeC6Le4VsAjCj1ZYFg8lq85H7jAGHeO3Z8WILJrCR65X+lgDUTwuyXW45fJOevHV82ZB+u9PNxguGhlwYIHSePhebLLgiVuhPg3UMbAqb5eV6vov8e/9SKBBY; _hal_tag=AGi9exQ3PzzdfuTxzgk/pAAwAHOCjfFRRPSXXH3LhTMnikA3SmLnnYVg7PUv7qwXIt7Wp5ZWPeIt2acB7I3duA==; _u_pa=%7B%22nrpt%22%3A0%7D; api_uid=Cm26mmfk81mYzW9K+Ky8Ag==; language=zh-Hans; currency=USD; __cf_bm=7Kh.4xcuZwEoafd31k7WG_AG3JFYXuRW.wHhw.2VY24-1753773658-*******-mbMS.k0PwymKod.a9IG7U88NP5QZ2h70vgNxiQNHWgdo8DkMrn6._jmPK925oOAn3Ewmii9n_qDWKZ_182OwK8_M2nHQTdAm8Eqe999qxxU; _bee=oGURgzZKyJtUmvWwYiF1DhvQKWuQkapT; _nano_fp=XpmYXq9ynqmynpTJXo_lhck5IrrPdZlFa8G0pr0b; _ttc=3.yMBn1kGOdLdl.1782194671; AccessToken=6DV5QPMIGRVBBS3V4ZCQE2IHR72J6LIXRGE42QSTW7B2QOFCVASQ0110d349e14b; dilx=~sXEue2kjh_DRO2g5Ou77; hfsc=L3yIeI437D/92pTJcA==; isLogin=1750658664596; njrpl=oGURgzZKyJtUmvWwYiF1DhvQKWuQkapT; user_uin=BAEZ47EIPAHX5GRSKFNCNSKVA733VJITLGPO2PCU;"""
    
    scraper.set_cookies_from_string(cookie_string)
    
    # 从URL提取商店ID
    url = "https://www.temu.com/us-zh-Hans/mug-love-m-634418221696617.html?goods_id=601100243104197"
    mall_id = scraper.extract_mall_id_from_url(url)
    
    if not mall_id:
        print("无法从URL中提取商店ID")
        return
    
    print(f"提取到商店ID: {mall_id}")
    
    # 获取数据
    print("正在获取商店信息和商品列表...")
    response_data = scraper.get_mall_info_and_goods(mall_id, page=1, page_size=20)
    
    if response_data:
        # 解析数据
        parsed_data = scraper.parse_response_data(response_data)
        
        if 'error' not in parsed_data:
            # 显示结果
            store_info = parsed_data.get('store_info', {})
            print(f"\n=== 商店信息 ===")
            print(f"商店名称: {store_info.get('store_name', 'N/A')}")
            print(f"粉丝数: {store_info.get('followers', 0)}")
            print(f"总销量: {store_info.get('total_sold', 0)}")
            print(f"评分: {store_info.get('rating', 0)}")
            
            products = parsed_data.get('products', [])
            print(f"\n=== 商品列表 ({len(products)}个商品) ===")
            for i, product in enumerate(products[:5], 1):  # 显示前5个
                print(f"{i}. {product.get('title', 'N/A')}")
                print(f"   价格: {product.get('price', 'N/A')}")
                print(f"   已售: {product.get('sold_num', 0)}")
                print(f"   评分: {product.get('rating', 0)}")
                print()
            
            # 保存完整数据
            with open('temu_data.json', 'w', encoding='utf-8') as f:
                json.dump(parsed_data, f, ensure_ascii=False, indent=2)
            print("完整数据已保存到 temu_data.json")
        else:
            print(f"数据解析失败: {parsed_data['error']}")
    else:
        print("获取数据失败")

if __name__ == "__main__":
    main()
