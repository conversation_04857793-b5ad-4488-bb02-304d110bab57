#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
安装Temu爬虫所需的依赖
"""

import subprocess
import sys
import os

def install_package(package):
    """安装Python包"""
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", package])
        print(f"✓ {package} 安装成功")
        return True
    except subprocess.CalledProcessError:
        print(f"✗ {package} 安装失败")
        return False

def check_chrome_driver():
    """检查ChromeDriver是否可用"""
    try:
        from selenium import webdriver
        from selenium.webdriver.chrome.options import Options
        
        options = Options()
        options.add_argument('--headless')
        options.add_argument('--no-sandbox')
        options.add_argument('--disable-dev-shm-usage')
        
        driver = webdriver.Chrome(options=options)
        driver.quit()
        print("✓ ChromeDriver 可用")
        return True
    except Exception as e:
        print(f"✗ ChromeDriver 不可用: {e}")
        print("请安装ChromeDriver:")
        print("1. 下载 ChromeDriver: https://chromedriver.chromium.org/")
        print("2. 将 chromedriver.exe 放到 PATH 环境变量中")
        print("3. 或者使用: pip install webdriver-manager")
        return False

def main():
    """主安装函数"""
    print("正在安装Temu爬虫依赖...")
    
    # 需要安装的包
    packages = [
        "requests",
        "selenium",
        "webdriver-manager",  # 自动管理ChromeDriver
        "beautifulsoup4",     # 备用HTML解析
        "lxml",              # XML解析器
    ]
    
    success_count = 0
    
    for package in packages:
        if install_package(package):
            success_count += 1
    
    print(f"\n安装完成: {success_count}/{len(packages)} 个包安装成功")
    
    # 检查ChromeDriver
    print("\n检查ChromeDriver...")
    if not check_chrome_driver():
        print("\n尝试自动安装ChromeDriver...")
        try:
            from webdriver_manager.chrome import ChromeDriverManager
            from selenium import webdriver
            from selenium.webdriver.chrome.service import Service
            
            # 自动下载并设置ChromeDriver
            service = Service(ChromeDriverManager().install())
            options = webdriver.ChromeOptions()
            options.add_argument('--headless')
            
            driver = webdriver.Chrome(service=service, options=options)
            driver.quit()
            print("✓ ChromeDriver 自动安装成功")
        except Exception as e:
            print(f"✗ ChromeDriver 自动安装失败: {e}")
    
    print("\n安装完成！现在可以运行爬虫了:")
    print("python temu_selenium_scraper.py")

if __name__ == "__main__":
    main()
