#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Temu API客户端
用于获取Temu商店信息和商品列表数据
"""

import requests
import json
import time
import random
from typing import Dict, List, Optional

class TemuAPIClient:
    def __init__(self):
        self.session = requests.Session()
        self.base_url = "https://www.temu.com"
        
        # 设置基础请求头
        self.headers = {
            'accept': 'application/json, text/plain, */*',
            'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8,zh-TW;q=0.7',
            'content-type': 'application/json;charset=UTF-8',
            'origin': 'https://www.temu.com',
            'priority': 'u=1, i',
            'referer': 'https://www.temu.com/',
            'sec-ch-ua': '"Chromium";v="134", "Not:A-Brand";v="24", "Google Chrome";v="134"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"Windows"',
            'sec-fetch-dest': 'empty',
            'sec-fetch-mode': 'cors',
            'sec-fetch-site': 'same-origin',
            'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/134.0.0.0 Safari/537.36'
        }
        
        self.session.headers.update(self.headers)
    
    def set_cookies(self, cookie_string: str):
        """
        设置Cookie字符串
        """
        cookies = {}
        for item in cookie_string.split('; '):
            if '=' in item:
                key, value = item.split('=', 1)
                cookies[key] = value
        
        self.session.cookies.update(cookies)
    
    def get_mall_info_with_goods_list(self, 
                                    mall_id: str,
                                    page_number: int = 1,
                                    page_size: int = 8,
                                    main_goods_ids: List[str] = None) -> Optional[Dict]:
        """
        获取商店信息和商品列表
        
        Args:
            mall_id: 商店ID
            page_number: 页码
            page_size: 每页数量
            main_goods_ids: 主商品ID列表
            
        Returns:
            API响应数据
        """
        if main_goods_ids is None:
            main_goods_ids = ["1"]
        
        # 生成随机的list_id（模拟真实请求）
        list_id = self._generate_list_id()
        
        payload = {
            "mallId": mall_id,
            "mainGoodsIds": main_goods_ids,
            "source_page_sn": "10013",
            "mall_id": mall_id,
            "main_goods_ids": main_goods_ids,
            "filter_items": "",
            "page_number": page_number,
            "page_size": page_size,
            "list_id": list_id,
            "scene_code": "mall_rule",
            "page_sn": 10040,
            "page_el_sn": 201265,
            "source": 10018,
            "anti_content": "1"
        }
        
        try:
            url = f"{self.base_url}/us-zh-Hans/api/bg/circle/c/mall/mallInfoWithGoodsList"
            response = self.session.post(url, json=payload, timeout=30)
            
            if response.status_code == 200:
                return response.json()
            else:
                print(f"请求失败，状态码: {response.status_code}")
                print(f"响应内容: {response.text}")
                return None
                
        except Exception as e:
            print(f"请求异常: {str(e)}")
            return None
    
    def _generate_list_id(self) -> str:
        """
        生成随机的list_id
        """
        chars = 'abcdefghijklmnopqrstuvwxyz0123456789'
        return ''.join(random.choice(chars) for _ in range(21))
    
    def extract_key_data(self, api_response: Dict) -> Dict:
        """
        从API响应中提取关键数据
        
        Args:
            api_response: API响应数据
            
        Returns:
            提取的关键数据
        """
        if not api_response or 'result' not in api_response:
            return {}
        
        result = api_response['result']
        extracted_data = {}
        
        # 提取商店基本信息
        if 'mallInfo' in result:
            mall_info = result['mallInfo']
            extracted_data['store_info'] = {
                'store_name': mall_info.get('mallName', ''),
                'followers': mall_info.get('followNum', 0),  # 粉丝数
                'total_sold': mall_info.get('soldNum', 0),   # 总销量
                'store_id': mall_info.get('mallId', ''),
                'store_logo': mall_info.get('mallLogo', ''),
                'description': mall_info.get('mallDesc', '')
            }
        
        # 提取商品列表
        if 'goodsList' in result:
            goods_list = result['goodsList']
            extracted_data['products'] = []
            
            for goods in goods_list:
                product = {
                    'product_id': goods.get('goodsId', ''),
                    'title': goods.get('goodsName', ''),
                    'price': goods.get('priceInfo', {}).get('price', ''),
                    'original_price': goods.get('priceInfo', {}).get('originalPrice', ''),
                    'image_url': goods.get('image', ''),
                    'sold_num': goods.get('soldNum', 0),  # 已售数量
                    'rating': goods.get('rating', 0),
                    'review_count': goods.get('reviewNum', 0),
                    'product_url': goods.get('goodsUrl', '')
                }
                extracted_data['products'].append(product)
        
        return extracted_data

def main():
    """
    示例使用
    """
    # 你的Cookie字符串（需要更新为最新的）
    cookie_string = """region=211; timezone=Asia%2FHong_Kong; gmp_temu_token=EVIPzCVqVuHegqxRTi6uTh+lCO4JjZ2iskrSNW9KC6utVrUN3EYeC6Le4VsAjCj1ZYFg8lq85H7jAGHeO3Z8WILJrCR65X+lgDUTwuyXW45fJOevHV82ZB+u9PNxguGhlwYIHSePhebLLgiVuhPg3UMbAqb5eV6vov8e/9SKBBY; _hal_tag=AGi9exQ3PzzdfuTxzgk/pAAwAHOCjfFRRPSXXH3LhTMnikA3SmLnnYVg7PUv7qwXIt7Wp5ZWPeIt2acB7I3duA==; _u_pa=%7B%22nrpt%22%3A0%7D; api_uid=Cm26mmfk81mYzW9K+Ky8Ag==; language=zh-Hans; currency=USD; __cf_bm=7Kh.4xcuZwEoafd31k7WG_AG3JFYXuRW.wHhw.2VY24-1753773658-*******-mbMS.k0PwymKod.a9IG7U88NP5QZ2h70vgNxiQNHWgdo8DkMrn6._jmPK925oOAn3Ewmii9n_qDWKZ_182OwK8_M2nHQTdAm8Eqe999qxxU; _bee=oGURgzZKyJtUmvWwYiF1DhvQKWuQkapT; _nano_fp=XpmYXq9ynqmynpTJXo_lhck5IrrPdZlFa8G0pr0b; _ttc=3.yMBn1kGOdLdl.1782194671; AccessToken=6DV5QPMIGRVBBS3V4ZCQE2IHR72J6LIXRGE42QSTW7B2QOFCVASQ0110d349e14b; dilx=~sXEue2kjh_DRO2g5Ou77; hfsc=L3yIeI437D/92pTJcA==; isLogin=1750658664596; njrpl=oGURgzZKyJtUmvWwYiF1DhvQKWuQkapT; user_uin=BAEZ47EIPAHX5GRSKFNCNSKVA733VJITLGPO2PCU;"""
    
    # 创建客户端
    client = TemuAPIClient()
    client.set_cookies(cookie_string)
    
    # 获取数据
    mall_id = "634418213167233"  # 从你的URL中提取的商店ID
    
    print("正在获取商店信息和商品列表...")
    response = client.get_mall_info_with_goods_list(mall_id, page_number=1, page_size=20)
    
    if response:
        print("API调用成功！")
        
        # 提取关键数据
        key_data = client.extract_key_data(response)
        
        if key_data:
            print("\n=== 商店信息 ===")
            store_info = key_data.get('store_info', {})
            print(f"商店名称: {store_info.get('store_name', 'N/A')}")
            print(f"粉丝数: {store_info.get('followers', 0)}")
            print(f"总销量: {store_info.get('total_sold', 0)}")
            
            print(f"\n=== 商品列表 ({len(key_data.get('products', []))}) ===")
            for i, product in enumerate(key_data.get('products', [])[:5], 1):  # 只显示前5个
                print(f"{i}. {product.get('title', 'N/A')}")
                print(f"   价格: {product.get('price', 'N/A')}")
                print(f"   已售: {product.get('sold_num', 0)}")
                print(f"   评分: {product.get('rating', 0)}")
                print()
        
        # 保存完整响应到文件
        with open('temu_response.json', 'w', encoding='utf-8') as f:
            json.dump(response, f, ensure_ascii=False, indent=2)
        print("完整响应已保存到 temu_response.json")
        
    else:
        print("API调用失败")

if __name__ == "__main__":
    main()
