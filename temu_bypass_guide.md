# Temu API 风控分析和绕过指南

## 1. 风控机制分析

### 当前遇到的问题
- **错误码**: 40001
- **HTTP状态码**: 403 Forbidden
- **含义**: 认证失败或权限不足

### 关键风控点

#### 1.1 Cookie验证
```
关键Cookie字段：
- AccessToken: 访问令牌（最重要）
- gmp_temu_token: Temu安全令牌
- api_uid: API用户标识
- user_uin: 用户唯一标识
- _nano_fp: 浏览器指纹
- _bee: 设备标识
- __cf_bm: Cloudflare Bot管理令牌
```

#### 1.2 请求头验证
```
必需的请求头：
- User-Agent: 必须是真实浏览器
- Referer: 必须来自temu.com域名
- Origin: 必须是https://www.temu.com
- sec-ch-ua系列: 浏览器客户端提示
- Anti-Content: 反爬虫标识
```

#### 1.3 参数验证
```
关键参数：
- list_id: 每次请求唯一
- anti_content: 反爬虫标识
- timestamp: 时间戳（可能用于签名）
- source/page_sn等: 页面追踪参数
```

## 2. 绕过策略

### 2.1 Cookie获取和更新
**问题**: 你提供的Cookie可能已过期或无效

**解决方案**:
1. 手动访问Temu网站，登录账号
2. 打开开发者工具 (F12)
3. 访问目标商店页面
4. 在Network标签中找到API请求
5. 复制最新的Cookie和请求头

### 2.2 请求签名
**问题**: Temu可能使用了请求签名验证

**解决方案**:
1. 分析JavaScript代码，找到签名算法
2. 或者直接从浏览器中复制完整的请求

### 2.3 浏览器自动化
**推荐方案**: 使用Selenium或Playwright模拟真实浏览器

## 3. 实施方案

### 方案A: 浏览器自动化 (推荐)
```python
from selenium import webdriver
from selenium.webdriver.common.by import By
import time
import json

def get_temu_data_with_selenium(url):
    driver = webdriver.Chrome()
    try:
        driver.get(url)
        time.sleep(5)  # 等待页面加载
        
        # 执行JavaScript获取数据
        script = """
        return new Promise((resolve) => {
            // 监听网络请求
            const originalFetch = window.fetch;
            window.fetch = function(...args) {
                return originalFetch.apply(this, args).then(response => {
                    if (args[0].includes('mallInfoWithGoodsList')) {
                        response.clone().json().then(data => {
                            window.temuData = data;
                        });
                    }
                    return response;
                });
            };
            
            // 触发数据加载
            setTimeout(() => {
                resolve(window.temuData || null);
            }, 10000);
        });
        """
        
        data = driver.execute_async_script(script)
        return data
    finally:
        driver.quit()
```

### 方案B: 请求拦截
```python
import requests
from mitmproxy import http
import json

# 使用mitmproxy拦截浏览器请求
def intercept_temu_requests():
    # 启动代理服务器
    # 配置浏览器使用代理
    # 拦截并记录API请求
    pass
```

### 方案C: 逆向工程
1. 分析Temu的JavaScript代码
2. 找到API调用逻辑
3. 复制签名算法
4. 实现完整的请求构造

## 4. 数据提取目标

根据你的需求，需要提取：

### 4.1 商店统计数据
- 粉丝数 (followers)
- 总销量 (total_sold)
- 商店评分 (rating)

### 4.2 商品列表数据
- 商品图片 (image_url)
- 商品标题 (title)
- 商品描述 (description)
- 价格信息 (price)
- 销量 (sold_num)
- 评分 (rating)

## 5. 实际操作步骤

### 步骤1: 获取最新Cookie
1. 打开Chrome浏览器
2. 访问: https://www.temu.com/us-zh-Hans/mug-love-m-634418221696617.html
3. 登录你的账号
4. 打开开发者工具 (F12)
5. 切换到Network标签
6. 刷新页面
7. 找到 `mallInfoWithGoodsList` 请求
8. 右键 -> Copy -> Copy as cURL

### 步骤2: 更新代码
将获取的cURL命令转换为Python代码

### 步骤3: 测试和调试
逐步调试请求参数，直到成功获取数据

## 6. 风险提示

1. **账号风险**: 频繁请求可能导致账号被封
2. **IP风险**: 可能需要使用代理IP
3. **法律风险**: 确保遵守网站服务条款
4. **技术风险**: 反爬虫机制可能随时更新

## 7. 建议

1. **使用官方API**: 如果Temu提供官方API，优先使用
2. **控制频率**: 添加请求间隔，避免过于频繁
3. **模拟真实用户**: 使用真实浏览器环境
4. **监控变化**: 定期检查和更新爬虫代码
