#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Temu API分析工具
分析风控机制和数据提取方法
"""

import requests
import json
import time
import random
import hashlib
from urllib.parse import urlparse, parse_qs
from typing import Dict, List, Optional

class TemuAnalyzer:
    def __init__(self):
        self.session = requests.Session()
        
    def analyze_url(self, url: str) -> Dict:
        """
        分析Temu URL，提取关键参数
        """
        parsed = urlparse(url)
        query_params = parse_qs(parsed.query)
        
        # 从URL路径中提取mall_id
        path_parts = parsed.path.split('-')
        mall_id = None
        for part in path_parts:
            if part.startswith('m-') or (part.startswith('m') and len(part) > 1 and part[1:].isdigit()):
                if part.startswith('m-'):
                    mall_id = part[2:]
                else:
                    mall_id = part[1:]
                break

        # 如果上面没找到，尝试另一种方式
        if not mall_id:
            import re
            # 查找类似 m-数字 的模式
            match = re.search(r'm-(\d+)', parsed.path)
            if match:
                mall_id = match.group(1)
        
        analysis = {
            'url': url,
            'domain': parsed.netloc,
            'path': parsed.path,
            'mall_id_from_path': mall_id,
            'goods_id': query_params.get('goods_id', [None])[0],
            'query_params': query_params
        }
        
        return analysis
    
    def analyze_cookies(self, cookie_string: str) -> Dict:
        """
        分析Cookie字符串，识别关键的风控参数
        """
        cookies = {}
        for item in cookie_string.split('; '):
            if '=' in item:
                key, value = item.split('=', 1)
                cookies[key] = value
        
        # 分析关键Cookie
        critical_cookies = {
            'AccessToken': cookies.get('AccessToken', ''),
            'gmp_temu_token': cookies.get('gmp_temu_token', ''),
            'api_uid': cookies.get('api_uid', ''),
            'user_uin': cookies.get('user_uin', ''),
            '_nano_fp': cookies.get('_nano_fp', ''),  # 浏览器指纹
            '_bee': cookies.get('_bee', ''),  # 设备标识
            '__cf_bm': cookies.get('__cf_bm', ''),  # Cloudflare bot管理
            'isLogin': cookies.get('isLogin', ''),
            'language': cookies.get('language', ''),
            'currency': cookies.get('currency', ''),
            'region': cookies.get('region', '')
        }
        
        analysis = {
            'total_cookies': len(cookies),
            'critical_cookies': critical_cookies,
            'has_login_token': bool(critical_cookies['AccessToken']),
            'has_device_fingerprint': bool(critical_cookies['_nano_fp']),
            'has_cloudflare_protection': bool(critical_cookies['__cf_bm']),
            'is_logged_in': critical_cookies['isLogin'] != '',
            'all_cookies': cookies
        }
        
        return analysis
    
    def generate_request_params(self, mall_id: str, page: int = 1, size: int = 20) -> Dict:
        """
        生成请求参数，模拟真实请求
        """
        # 生成随机list_id
        chars = 'abcdefghijklmnopqrstuvwxyz0123456789'
        list_id = ''.join(random.choice(chars) for _ in range(21))
        
        # 生成时间戳相关参数
        timestamp = int(time.time() * 1000)
        
        params = {
            "mallId": mall_id,
            "mainGoodsIds": ["1"],
            "source_page_sn": "10013",
            "mall_id": mall_id,
            "main_goods_ids": ["1"],
            "filter_items": "",
            "page_number": page,
            "page_size": size,
            "list_id": list_id,
            "scene_code": "mall_rule",
            "page_sn": 10040,
            "page_el_sn": 201265,
            "source": 10018,
            "anti_content": "1",
            "timestamp": timestamp
        }
        
        return params
    
    def detect_anti_bot_measures(self, response: requests.Response) -> Dict:
        """
        检测反爬虫措施
        """
        measures = {
            'status_code': response.status_code,
            'has_captcha': False,
            'has_rate_limit': False,
            'has_cloudflare_challenge': False,
            'has_access_denied': False,
            'response_size': len(response.content),
            'content_type': response.headers.get('content-type', ''),
            'server': response.headers.get('server', ''),
            'cf_ray': response.headers.get('cf-ray', ''),
            'response_headers': dict(response.headers)
        }
        
        # 检查响应内容
        content = response.text.lower()
        
        if 'captcha' in content or 'verify' in content:
            measures['has_captcha'] = True
        
        if 'rate limit' in content or 'too many requests' in content:
            measures['has_rate_limit'] = True
        
        if 'cloudflare' in content and 'checking' in content:
            measures['has_cloudflare_challenge'] = True
        
        if 'access denied' in content or 'forbidden' in content:
            measures['has_access_denied'] = True
        
        return measures
    
    def test_api_endpoint(self, mall_id: str, cookies: str) -> Dict:
        """
        测试API端点
        """
        headers = {
            'accept': 'application/json, text/plain, */*',
            'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8,zh-TW;q=0.7',
            'content-type': 'application/json;charset=UTF-8',
            'origin': 'https://www.temu.com',
            'referer': 'https://www.temu.com/',
            'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/134.0.0.0 Safari/537.36'
        }
        
        # 设置cookies
        cookie_dict = {}
        for item in cookies.split('; '):
            if '=' in item:
                key, value = item.split('=', 1)
                cookie_dict[key] = value
        
        # 生成请求参数
        params = self.generate_request_params(mall_id)
        
        try:
            url = "https://www.temu.com/us-zh-Hans/api/bg/circle/c/mall/mallInfoWithGoodsList"
            response = self.session.post(
                url, 
                json=params, 
                headers=headers, 
                cookies=cookie_dict,
                timeout=30
            )
            
            # 分析响应
            anti_bot = self.detect_anti_bot_measures(response)
            
            result = {
                'success': response.status_code == 200,
                'status_code': response.status_code,
                'anti_bot_measures': anti_bot,
                'response_preview': response.text[:500] if response.text else '',
                'request_params': params
            }
            
            if response.status_code == 200:
                try:
                    json_data = response.json()
                    result['has_valid_json'] = True
                    result['response_structure'] = self._analyze_json_structure(json_data)
                except:
                    result['has_valid_json'] = False
            
            return result
            
        except Exception as e:
            return {
                'success': False,
                'error': str(e),
                'request_params': params
            }
    
    def _analyze_json_structure(self, data: Dict) -> Dict:
        """
        分析JSON响应结构
        """
        structure = {
            'top_level_keys': list(data.keys()) if isinstance(data, dict) else [],
            'has_result': 'result' in data if isinstance(data, dict) else False,
            'has_error': 'error' in data if isinstance(data, dict) else False
        }
        
        if structure['has_result'] and isinstance(data.get('result'), dict):
            result = data['result']
            structure['result_keys'] = list(result.keys())
            structure['has_mall_info'] = 'mallInfo' in result
            structure['has_goods_list'] = 'goodsList' in result
            
            if 'goodsList' in result and isinstance(result['goodsList'], list):
                structure['goods_count'] = len(result['goodsList'])
        
        return structure

def main():
    """
    主分析函数
    """
    analyzer = TemuAnalyzer()
    
    # 分析URL
    url = "https://www.temu.com/us-zh-Hans/mug-love-m-634418221696617.html?goods_id=601100243104197"
    url_analysis = analyzer.analyze_url(url)
    
    print("=== URL分析 ===")
    print(f"商店ID: {url_analysis['mall_id_from_path']}")
    print(f"商品ID: {url_analysis['goods_id']}")
    print()
    
    # 分析Cookie
    cookie_string = """region=211; timezone=Asia%2FHong_Kong; gmp_temu_token=EVIPzCVqVuHegqxRTi6uTh+lCO4JjZ2iskrSNW9KC6utVrUN3EYeC6Le4VsAjCj1ZYFg8lq85H7jAGHeO3Z8WILJrCR65X+lgDUTwuyXW45fJOevHV82ZB+u9PNxguGhlwYIHSePhebLLgiVuhPg3UMbAqb5eV6vov8e/9SKBBY; _hal_tag=AGi9exQ3PzzdfuTxzgk/pAAwAHOCjfFRRPSXXH3LhTMnikA3SmLnnYVg7PUv7qwXIt7Wp5ZWPeIt2acB7I3duA==; _u_pa=%7B%22nrpt%22%3A0%7D; api_uid=Cm26mmfk81mYzW9K+Ky8Ag==; language=zh-Hans; currency=USD; __cf_bm=7Kh.4xcuZwEoafd31k7WG_AG3JFYXuRW.wHhw.2VY24-1753773658-*******-mbMS.k0PwymKod.a9IG7U88NP5QZ2h70vgNxiQNHWgdo8DkMrn6._jmPK925oOAn3Ewmii9n_qDWKZ_182OwK8_M2nHQTdAm8Eqe999qxxU; _bee=oGURgzZKyJtUmvWwYiF1DhvQKWuQkapT; _nano_fp=XpmYXq9ynqmynpTJXo_lhck5IrrPdZlFa8G0pr0b; _ttc=3.yMBn1kGOdLdl.1782194671; AccessToken=6DV5QPMIGRVBBS3V4ZCQE2IHR72J6LIXRGE42QSTW7B2QOFCVASQ0110d349e14b; dilx=~sXEue2kjh_DRO2g5Ou77; hfsc=L3yIeI437D/92pTJcA==; isLogin=1750658664596; njrpl=oGURgzZKyJtUmvWwYiF1DhvQKWuQkapT; user_uin=BAEZ47EIPAHX5GRSKFNCNSKVA733VJITLGPO2PCU;"""
    
    cookie_analysis = analyzer.analyze_cookies(cookie_string)
    
    print("=== Cookie分析 ===")
    print(f"总Cookie数量: {cookie_analysis['total_cookies']}")
    print(f"已登录: {cookie_analysis['is_logged_in']}")
    print(f"有访问令牌: {cookie_analysis['has_login_token']}")
    print(f"有设备指纹: {cookie_analysis['has_device_fingerprint']}")
    print(f"有Cloudflare保护: {cookie_analysis['has_cloudflare_protection']}")
    print()
    
    # 测试API
    mall_id = url_analysis['mall_id_from_path']
    if mall_id:
        print("=== API测试 ===")
        api_test = analyzer.test_api_endpoint(mall_id, cookie_string)
        
        print(f"请求成功: {api_test['success']}")
        print(f"状态码: {api_test['status_code']}")
        
        if 'anti_bot_measures' in api_test:
            measures = api_test['anti_bot_measures']
            print(f"检测到验证码: {measures['has_captcha']}")
            print(f"检测到限流: {measures['has_rate_limit']}")
            print(f"检测到Cloudflare挑战: {measures['has_cloudflare_challenge']}")
        
        if api_test.get('has_valid_json'):
            structure = api_test['response_structure']
            print(f"有效JSON响应: {structure['has_mall_info'] and structure['has_goods_list']}")
            if structure.get('goods_count'):
                print(f"商品数量: {structure['goods_count']}")
        
        # 保存测试结果
        with open('api_test_result.json', 'w', encoding='utf-8') as f:
            json.dump(api_test, f, ensure_ascii=False, indent=2)
        print("\n测试结果已保存到 api_test_result.json")

if __name__ == "__main__":
    main()
